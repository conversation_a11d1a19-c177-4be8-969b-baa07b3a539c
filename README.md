[![Review Assignment Due Date](https://classroom.github.com/assets/deadline-readme-button-22041afd0340ce965d47ae6ef1cefeee28c7c493a6346c4f15d667ab976d596c.svg)](https://classroom.github.com/a/idxPpgnz)
![School of Solana](https://github.com/Ackee-Blockchain/school-of-solana/blob/master/.banner/banner.png?raw=true)

## 📚Solana Program
We are about halfway through the course, and you already have some experience with programming on Solana. It is time to create something on your own! You will be building a dApp that will serve as the culmination of everything you have learned so far. Feel free to implement whatever comes to your mind, (as long as it passes the requirements).

**This does not mean that the School of Solana is coming to an end just yet!** There are still several exciting lectures ahead, as well as one security related task.

### Task details
This task consists of two parts:
1. **Core of your dApp**
    - A deployed Solana program.
2. **Frontend**
    - A simple frontend to interact with the dApp.

### Requirements
- An Anchor program deployed on **Devnet** or **Mainnet**.
- The Anchor program must use a PDA (Program Derived Address).
- At least one TypeScript **test** for each Anchor program instruction. These tests should cover both **happy** and **unhappy** (intentional error-triggering) scenarios.
- A simple **frontend** deployed using your preferred provider (for more info, check below).
- A filled out **PROJECT_DESCRIPTION.md** file.

### Ideas
We highly recommend starting with something simple. Take time to think through your project and work on it in iterations. Do not try to implement everything at once!

Below is a list of few ideas to get you started:
- **Social app**
    - Instagram
    - Giphy
    - Friendtech
    - Spotify
- **Blog**
- **Voting** ([D21 - Janeček method](https://www.ih21.org/en/guidelines))
- **DeFi**
    - Crowdfunding
    - Raffles
    - Escrow
    - Tipping
    - Lending ([Save Documentation](https://docs.save.finance/))
    - Liquid Staking ([Marinade Documentation](https://docs.marinade.finance/))
    - Data Query with Pyth ([Pyth Documentation](https://docs.pyth.network/price-feeds))
    - AMM ([Raydium Documentation](https://raydium.gitbook.io/raydium/))
- **Gaming**
    - Browser Game ([Gaming on Solana](https://solanacookbook.com/gaming/nfts-in-games.html#nfts-in-games))

### Deadline
The deadline for this task is **Wednesday, August 27th, at 23:59 UTC**.
>[!CAUTION]
>Note that we will not accept submissions after the deadline.

### Submission
There are two folders, one for the Anchor project, and one for the frontend. Push your changes to the **main** branch of **this** repository.

>[!IMPORTANT]
>It is essential that you fill out the `PROJECT_DESCRIPTION.md` template completely and accurately. This document will be used by AI for the initial evaluation, so provide detailed information about your project, including working links, clear descriptions, and technical implementation details.

### Evaluation
The evaluation process is based on the **requirements**. If you meet the requirements, you pass the task!

>[!NOTE]
>We have a record number of participants this season, so the first round of evaluations will be conducted by AI to verify requirements before manual review. AI can make mistakes. If you believe you fulfilled all requirements but weren't graded correctly, please create a support ticket and we will resolve the issue.

>[!CAUTION]
>We expect original work that demonstrates your understanding and creativity. While you may draw inspiration from examples covered in lessons and tasks, **direct copying is not acceptable**. If you choose to build upon an example from the School of Solana materials, you must significantly expand it with additional features, instructions, and functionality to showcase your learning progress. 

### Example Workflow
Let's say you are going to implement the Twitter dApp as the Solana Program. Here's how the steps could look:

**1.** Implement Twitter dApp using the Anchor framework.

**2.** Test the Twitter dApp using the Anchor framework.

**3.** Deploy the Twitter dApp on the Solana Devnet.

**4.** Using the create solana dapp template, implement frontend for the Twitter dApp.

**5.** Publish Frontend using [Vercel](https://vercel.com).

**6.** Fill out the PROJECT_DESCRIPTION.md template.

**7.** Submit the Twitter dApp using GitHub Classroom.

### Useful Links
- [Vercel](https://vercel.com)
- [Create Solana Dapp](https://github.com/solana-foundation/create-solana-dapp)
- [Account Macro Constraints](https://docs.rs/anchor-lang/latest/anchor_lang/derive.Accounts.html#constraints)
- [Solana Developers Courses](https://solana.com/developers/courses)

-----

### Need help?
>[!TIP]
>If you have any questions, feel free to reach out to us on [Discord](https://discord.gg/z3JVuZyFnp).
