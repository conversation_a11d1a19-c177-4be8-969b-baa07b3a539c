{"name": "vesting", "scripts": {"anchor": "cd anchor && anchor", "anchor-build": "cd anchor && anchor build", "anchor-localnet": "cd anchor && anchor localnet", "anchor-test": "cd anchor && anchor test", "build": "next build", "ci": "npm run build && npm run lint && npm run format:check", "dev": "next dev --turbopack", "format": "prettier --write .", "format:check": "prettier --check .", "lint": "next lint", "start": "next start"}, "description": "Next.js, <PERSON><PERSON><PERSON>, @solana/web3.js, Wallet Adapter, basic Anchor program", "keywords": ["anchor-basic", "nextjs", "react", "solana-web3js", "tailwind", "typescript", "wallet-adapter"], "version": "0.0.0", "private": true, "dependencies": {"@coral-xyz/anchor": "^0.31.1", "@heroicons/react": "^2.2.0", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@solana/spl-token": "0.4.13", "@solana/wallet-adapter-base": "0.9.27", "@solana/wallet-adapter-react": "0.15.39", "@solana/wallet-adapter-react-ui": "0.9.39", "@solana/web3.js": "^1.98.4", "@tanstack/react-query": "^5.85.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jotai": "^2.13.1", "lucide-react": "^0.540.0", "next": "15.5.0", "next-themes": "^0.4.6", "react": "^19.1.1", "react-dom": "^19.1.1", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.7"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.12", "@types/bn.js": "^5.2.0", "@types/jest": "^30.0.0", "@types/node": "^24.3.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "eslint": "^9.33.0", "eslint-config-next": "15.5.0", "jest": "^30.0.5", "prettier": "^3.6.2", "tailwindcss": "^4.1.12", "ts-jest": "^29.4.1", "typescript": "^5.9.2"}}