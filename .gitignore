# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules/
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/frontend/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem
*lock.*

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Anchor
target/
/anchor/.anchor
/anchor/target/debug
/anchor/target/deploy
/anchor/target/release
/anchor/target/sbf-solana-solana
/anchor/target/test-ledger
/anchor/target/.rustc_info.json
test-ledger
**/*.rs.bk
