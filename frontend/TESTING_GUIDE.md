# Solana Vesting Frontend Testing Guide

## Overview
This guide provides step-by-step instructions for testing the Solana Vesting frontend application with a deployed program on Devnet.

## Prerequisites Setup

### Program Information
- **Program ID (Devnet):** `Com4VLuPH372ZMUJA62GcwYUMpxRBPRu6ByVHRG9DVRE`
- **Test Token Mint:** `9vDQXUf8uxFTYe1zq1kbFSoFT7YVnMPX7kkXJ7sEHjCW`
- **Token Account:** `2eNMvatSv3DjA4xnJUNATaxCHifpxmPjTuWue4BmoM5R`
- **Test Wallet:** `9vErBtkwYRrXU86Q5wgRp8WWcckaPhYANgjEJ4z4DD2J`

### Required Setup Commands
```bash
# Set Solana CLI to devnet
solana config set --url devnet

# Check your balance (you need SOL for transaction fees)
solana balance

# Create a test token (if needed)
spl-token create-token --decimals 6

# Create token account and mint tokens
spl-token create-account <TOKEN_MINT>
spl-token mint <TOKEN_MINT> 1000000
```

## Step-by-Step Testing Process

### Step 1: Start the Application
1. **Start the development server:**
   ```bash
   npm run dev
   ```
2. **Open browser** and navigate to: http://localhost:3000
3. **Click "Vesting Program"** in the navigation menu

### Step 2: Connect Your Wallet
1. **Click "Connect Wallet"** button
2. **Select your preferred wallet** (Phantom, Solflare, etc.)
3. **Ensure you're connected to Devnet** in your wallet settings
4. **Verify the cluster** shows "devnet" in the top-right corner

### Step 3: Create a Vesting Account
1. **Fill in the form:**
   - **Company Name:** `TestCompany` (or any name you prefer)
   - **Token Mint Address:** `9vDQXUf8uxFTYe1zq1kbFSoFT7YVnMPX7kkXJ7sEHjCW`

2. **Click "Create New Vesting Account"**
3. **Approve the transaction** in your wallet
4. **Wait for confirmation** - you should see a success toast notification

### Step 4: Create Employee Vesting Schedule
After the vesting account is created, you'll see a card with your company name:

1. **Fill in the employee vesting form:**
   - **Beneficiary Address:** Use a different wallet address for testing
   - **Start Time:** Current Unix timestamp (e.g., `**********`)
   - **End Time:** Future Unix timestamp (e.g., `**********`)
   - **Cliff Time:** Cliff timestamp (e.g., `**********`)
   - **Total Allocation:** `100000` (100,000 tokens)

2. **Click "Create Employee Vesting Account"**
3. **Approve the transaction** in your wallet
4. **Wait for confirmation**

### Step 5: Test Token Claiming (Optional)
1. **Wait until after the cliff time** (or use past timestamps for testing)
2. **Switch to the beneficiary wallet** in your wallet app
3. **Click "Claim Tokens"**
4. **Approve the transaction**

## Useful Information

### Unix Timestamp Examples
Use https://www.unixtimestamp.com/ for conversions:
- **Current time:** ~`**********` (Jan 2025)
- **1 month later:** ~`**********` (Feb 2025)
- **1 year later:** ~`**********` (Jan 2026)

### Verification Commands
```bash
# Check program deployment
solana program show Com4VLuPH372ZMUJA62GcwYUMpxRBPRu6ByVHRG9DVRE

# Monitor program logs
solana logs Com4VLuPH372ZMUJA62GcwYUMpxRBPRu6ByVHRG9DVRE --url devnet

# Check account details
solana account <ACCOUNT_ADDRESS> --url devnet
```

### Solana Explorer
- **Devnet Explorer:** https://explorer.solana.com/?cluster=devnet
- Search for transaction signatures to verify program interactions

## Troubleshooting

### Common Issues
1. **"Program account not found"**
   - Check cluster selector (should be "devnet")
   - Verify program ID is correct

2. **Transaction fails**
   - Ensure sufficient SOL balance for fees
   - Check wallet is connected to devnet

3. **Wallet connection issues**
   - Refresh the page
   - Try different wallet or browser

4. **JavaScript errors**
   - Check browser console for error messages
   - Verify all dependencies are installed

### Debug Steps
1. **Check browser console** for JavaScript errors
2. **Verify wallet network** is set to devnet
3. **Confirm SOL balance** for transaction fees
4. **Check transaction signatures** in Solana Explorer

## Expected Results

After successful testing, you should see:
- ✅ Vesting account created with company name
- ✅ Employee vesting schedule created with specified parameters
- ✅ Ability to claim tokens (after cliff time)
- ✅ Real-time UI updates after transactions
- ✅ Transaction signatures in toast notifications

## Additional Notes

### Token Decimals
- Test token uses 6 decimals
- Input amounts are in base units (e.g., 100000 = 0.1 tokens)

### Time Considerations
- All timestamps are Unix timestamps (seconds since epoch)
- Cliff time must be between start and end time
- Current time must be after cliff time for claiming

### Security Notes
- This is for testing purposes only
- Use test wallets and small amounts
- Never use mainnet for testing

---

**Last Updated:** January 2025
**Program Version:** v0.1.0
**Frontend Framework:** Next.js + Anchor
