"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VESTING_PROGRAM_ID = exports.VestingIDL = void 0;
exports.getVestingProgram = getVestingProgram;
exports.getVestingProgramId = getVestingProgramId;
// Here we export some useful types and functions for interacting with the Anchor program.
const anchor_1 = require("@coral-xyz/anchor");
const web3_js_1 = require("@solana/web3.js");
const vesting_json_1 = __importDefault(require("../target/idl/vesting.json"));
exports.VestingIDL = vesting_json_1.default;
// The programId is imported from the program IDL.
exports.VESTING_PROGRAM_ID = new web3_js_1.PublicKey(vesting_json_1.default.address);
// This is a helper function to get the Vesting Anchor program.
function getVestingProgram(provider) {
    return new anchor_1.Program(vesting_json_1.default, provider);
}
// This is a helper function to get the program ID for the Vesting program depending on the cluster.
function getVestingProgramId(cluster) {
    switch (cluster) {
        case 'devnet':
        case 'testnet':
            // This is the program ID for the Vesting program on devnet and testnet.
            return new web3_js_1.PublicKey('Com4VLuPH372ZMUJA62GcwYUMpxRBPRu6ByVHRG9DVRE');
        case 'mainnet-beta':
        default:
            return exports.VESTING_PROGRAM_ID;
    }
}
//# sourceMappingURL=vesting-exports.js.map