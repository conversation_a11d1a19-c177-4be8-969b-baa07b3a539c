// Anchor program utilities and types
import { AnchorProvider, Program } from '@coral-xyz/anchor';
import { Cluster, PublicKey } from '@solana/web3.js';

// Import the generated IDL and types
import VestingIDL from './vesting-idl.json';
import type { Vesting } from './vesting-types';

// Re-export the generated IDL and type
export { Vesting, VestingIDL };

// The programId is imported from the program IDL.
export const VESTING_PROGRAM_ID = new PublicKey(VestingIDL.address);

// This is a helper function to get the Vesting Anchor program.
export function getVestingProgram(provider: AnchorProvider): Program<Vesting> {
  return new Program(VestingIDL as Vesting, provider);
}

// This is a helper function to get the program ID for the Vesting program depending on the cluster.
export function getVestingProgramId(cluster: Cluster) {
  switch (cluster) {
    case 'devnet':
    case 'testnet':
    case 'mainnet-beta':
    default:
      // Use the deployed program ID from the IDL
      return VESTING_PROGRAM_ID;
  }
}
