import { AnchorProvider, Program } from '@coral-xyz/anchor';
import { Cluster, PublicKey } from '@solana/web3.js';
import VestingIDL from '../target/idl/vesting.json';
import type { Vesting } from '../target/types/vesting';
export { Vesting, VestingIDL };
export declare const VESTING_PROGRAM_ID: PublicKey;
export declare function getVestingProgram(provider: AnchorProvider): Program<Vesting>;
export declare function getVestingProgramId(cluster: Cluster): PublicKey;
//# sourceMappingURL=vesting-exports.d.ts.map